"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { X } from "lucide-react"

interface Product {
  id: string
  name: string
  capacity: string
  price: number
  images: string[]
}

interface SizeComparisonProps {
  currentProduct: Product
  onClose: () => void
}

const allProducts = [
  {
    id: "yeye-go",
    name: "Yeye GO™",
    capacity: "10L",
    price: 199,
    dimensions: "25 × 18 × 20 cm",
    weight: "1.2 kg",
    image: "/images/cooler-lime-studio.jpg",
    scale: 0.6,
  },
  {
    id: "yeye-classic",
    name: "Yeye CLASSIC™",
    capacity: "18L",
    price: 299,
    dimensions: "35 × 25 × 28 cm",
    weight: "2.1 kg",
    image: "/images/cooler-minimal.png",
    scale: 1,
  },
  {
    id: "yeye-weekender",
    name: "Yeye WEEKENDER™",
    capacity: "35L",
    price: 399,
    dimensions: "45 × 32 × 35 cm",
    weight: "3.2 kg",
    image: "/images/cooler-family.png",
    scale: 1.4,
  },
]

export function SizeComparison({ currentProduct, onClose }: SizeComparisonProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-background rounded-2xl p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Size Comparison</h2>
            <p className="text-muted-foreground">Compare all Yeye cooler sizes</p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Visual Comparison */}
        <div className="mb-8 p-8 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20 rounded-xl">
          <div className="flex items-end justify-center gap-8 min-h-[300px]">
            {allProducts.map((product) => (
              <motion.div
                key={product.id}
                className={`text-center ${currentProduct.id === product.id ? "ring-2 ring-primary rounded-lg p-2" : ""}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * allProducts.indexOf(product) }}
              >
                <div
                  className="relative mb-4"
                  style={{
                    transform: `scale(${product.scale})`,
                    transformOrigin: "bottom center",
                  }}
                >
                  <img
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    className="w-32 h-32 object-contain mx-auto"
                  />
                </div>
                <h3 className="font-semibold text-sm">{product.name}</h3>
                <p className="text-xs text-muted-foreground">{product.capacity}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Detailed Comparison Table */}
        <div className="grid gap-4">
          {allProducts.map((product) => (
            <Card key={product.id} className={currentProduct.id === product.id ? "ring-2 ring-primary" : ""}>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <img
                    src={product.image || "/placeholder.svg"}
                    alt={product.name}
                    className="w-16 h-16 object-contain rounded-lg"
                  />
                  <div className="flex-1 grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <h3 className="font-semibold">{product.name}</h3>
                      <p className="text-sm text-muted-foreground">${product.price}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Capacity</p>
                      <p className="text-sm text-muted-foreground">{product.capacity}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Dimensions</p>
                      <p className="text-sm text-muted-foreground">{product.dimensions}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Weight</p>
                      <p className="text-sm text-muted-foreground">{product.weight}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Real-world Comparisons */}
        <div className="mt-8 p-6 bg-muted/50 rounded-xl">
          <h3 className="font-semibold mb-4">Real-world Size References</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-primary">Yeye GO™ (10L)</h4>
              <p className="text-muted-foreground">Similar to a large lunch box or small backpack</p>
            </div>
            <div>
              <h4 className="font-medium text-primary">Yeye CLASSIC™ (18L)</h4>
              <p className="text-muted-foreground">About the size of a carry-on suitcase</p>
            </div>
            <div>
              <h4 className="font-medium text-primary">Yeye WEEKENDER™ (35L)</h4>
              <p className="text-muted-foreground">Comparable to a large duffel bag</p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
