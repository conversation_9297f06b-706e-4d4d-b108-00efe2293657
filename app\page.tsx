"use client"

import { useState } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { OptimizedImage, HeroImage, ProductImage } from "@/components/ui/optimized-image"
import { ColorToggle } from "@/components/ui/color-toggle"
import { SiteHeader } from "@/components/layout/site-header"
import { ShoppingCart, Play, Star, Truck, RotateCcw, Shield, Heart, ChevronRight } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { useColorVariant } from "@/hooks/use-color-variant"
import { useProducts } from "@/hooks/use-api"
import Link from "next/link"
import type { Product } from "@/types"

const lifestyleImages = [
  { src: "/images/hero-beach.jpg", alt: "Beach lifestyle" },
  { src: "/images/pool-party.jpg", alt: "Pool party" },
  { src: "/images/sunset-dock.jpg", alt: "Sunset dock" },
  { src: "/images/group-picnic.jpg", alt: "Group picnic" },
  { src: "/images/yeye-jug-lifestyle.jpg", alt: "JUG lifestyle" },
]

export default function HomePage() {
  const [selectedProduct, setSelectedProduct] = useState("yeye-classic")
  const { addToCart } = useCart()
  const { variant } = useColorVariant()
  const { scrollY } = useScroll()
  const { data: products, loading } = useProducts()

  const heroY = useTransform(scrollY, [0, 500], [0, -150])
  const heroOpacity = useTransform(scrollY, [0, 300], [1, 0])

  const featuredProducts = products?.slice(0, 3) || []
  const selectedProductData = featuredProducts.find((p: Product) => p.id === selectedProduct) || featuredProducts[0]

  const handleAddToCart = (product: Product) => {
    const imageToUse = variant === "pink" && product.pinkImages?.[0] ? product.pinkImages[0] : product.images[0]

    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: imageToUse,
      quantity: 1,
    })
  }

   // ADD THIS LINE
  console.log("Current State:", { products, variant, loading })

  const getProductImage = (product: Product, index = 0) => {
    if (variant === "pink" && product.pinkImages?.[index]) {
      return product.pinkImages[index]
    }
    return product.images[index] || "/placeholder.svg?height=400&width=400&text=Loading"
  }

  const getFamilyImage = () => {
    return variant === "pink" ? "/images/cooler-family-pink.png" : "/images/cooler-family.png"
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <SiteHeader />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <SiteHeader />

      {/* Color Toggle - Fixed Position */}
      <div className="fixed top-20 right-4 z-50">
        <ColorToggle />
      </div>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <motion.div style={{ y: heroY, opacity: heroOpacity }} className="absolute inset-0 z-0">
          <HeroImage src="/images/hero-beach.jpg" alt="Yeye cooler on beach" className="object-cover w-full h-full" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative z-10 text-center text-white px-4"
        >
          <motion.h1
            className="text-4xl sm:text-6xl lg:text-8xl font-bold mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Cooler, but clearer.
          </motion.h1>
          <motion.p
            className="text-xl sm:text-2xl mb-8 max-w-3xl mx-auto opacity-90"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            The revolutionary transparent cooler collection that puts your style on display.
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Button
              size="lg"
              className="text-lg px-8 py-4 bg-primary text-primary-foreground hover:bg-primary/90"
              onClick={() => document.getElementById("products")?.scrollIntoView({ behavior: "smooth" })}
            >
              <ShoppingCart className="h-5 w-5 mr-2" />
              Shop Collection
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20"
            >
              <Play className="h-5 w-5 mr-2" />
              Watch Video
            </Button>
          </motion.div>
        </motion.div>
      </section>

      {/* Social Proof */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          >
            <div>
              <div className="text-3xl font-bold mb-2 text-foreground">50K+</div>
              <div className="text-muted-foreground">Happy Customers</div>
            </div>
            <div>
              <div className="flex items-center justify-center mb-2">
                <span className="text-3xl font-bold mr-2 text-foreground">4.9</span>
                <Star className="h-6 w-6 fill-yellow-400 text-yellow-400" />
              </div>
              <div className="text-muted-foreground">Average Rating</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2 text-foreground">98%</div>
              <div className="text-muted-foreground">Would Recommend</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2 text-foreground">2 Year</div>
              <div className="text-muted-foreground">Warranty</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Product Collection */}
      <section id="products" className="py-24 bg-background">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">The Yeye Collection</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Three sizes, one transparent vision. Find the perfect cooler for your lifestyle.
            </p>
            {variant === "pink" && (
              <Badge className="mt-4 bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-200 border-pink-200 dark:border-pink-800">
                Now showing Pink Edition
              </Badge>
            )}
          </motion.div>

          <div className="mb-12">
            <motion.div
              key={variant}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <OptimizedImage
                src={getFamilyImage()}
                alt={`Yeye cooler family ${variant === "pink" ? "- Pink Edition" : ""}`}
                width={1200}
                height={600}
                className="w-full max-w-4xl mx-auto rounded-3xl aspect-[2/1]" // Added aspect ratio for consistent sizing
              />
            </motion.div>
          </div>

          {selectedProductData && (
            <Tabs value={selectedProduct} onValueChange={setSelectedProduct} className="w-full">
              <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto mb-12 bg-muted">
                {featuredProducts.map((product: Product) => (
                  <TabsTrigger
                    key={product.id}
                    value={product.id}
                    className="data-[state=active]:bg-background data-[state=active]:text-foreground"
                  >
                    {product.name.split(" ")[1]}
                  </TabsTrigger>
                ))}
              </TabsList>

              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  key={`${selectedProductData.id}-${variant}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative"
                >
                  <div className="aspect-square rounded-3xl overflow-hidden">
                    <ProductImage
                      src={getProductImage(selectedProductData)}
                      alt={`${selectedProductData.name} ${variant === "pink" ? "- Pink Edition" : ""}`}
                      width={600} // Added explicit width/height for img tag
                      height={600}
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <div className="absolute top-4 left-4">
                    <Badge
                      className={variant === "pink" ? "bg-pink-500 text-white" : "bg-primary text-primary-foreground"}
                    >
                      {selectedProductData.badge}
                    </Badge>
                  </div>
                </motion.div>

                <motion.div
                  key={selectedProduct}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-6"
                >
                  <div>
                    <h3 className="text-4xl font-bold mb-2 text-foreground">
                      {selectedProductData.name}
                      {variant === "pink" && <span className="text-pink-500 ml-2">Pink</span>}
                    </h3>
                    <p className="text-xl text-muted-foreground mb-4">{selectedProductData.description}</p>
                    <div className="text-2xl font-bold mb-2 text-foreground">{selectedProductData.capacity}</div>
                  </div>

                  <ul className="space-y-2">
                    {selectedProductData.features.slice(0, 3).map((feature: string, index: number) => (
                      <li key={index} className="flex items-center gap-2">
                        <div
                          className={`w-1.5 h-1.5 rounded-full ${variant === "pink" ? "bg-pink-500" : "bg-primary"}`}
                        />
                        <span className="text-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <span className="text-3xl font-bold text-foreground">${selectedProductData.price}</span>
                      <span className="text-xl text-muted-foreground line-through">
                        ${selectedProductData.originalPrice}
                      </span>
                    </div>
                    <Badge variant="destructive">
                      Save{" "}
                      {Math.round(
                        ((selectedProductData.originalPrice - selectedProductData.price) /
                          selectedProductData.originalPrice) *
                          100,
                      )}
                      %
                    </Badge>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      size="lg"
                      className={`flex-1 ${variant === "pink" ? "bg-pink-500 hover:bg-pink-600" : "bg-primary hover:bg-primary/90"} text-white`}
                      onClick={() => handleAddToCart(selectedProductData)}
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      Preorder {selectedProductData.name.split(" ")[1]}
                      {variant === "pink" && " Pink"}
                    </Button>
                    <Button variant="outline" size="lg" className="border-border hover:bg-accent bg-transparent">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>

                  <Button variant="ghost" asChild className="w-full text-muted-foreground hover:text-foreground">
                    <Link href={`/products/${selectedProduct}`}>
                      View Full Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </motion.div>
              </div>
            </Tabs>
          )}
        </div>
      </section>

      {/* Core Technology */}
      <section className="py-24 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">Transparency Redefined</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Every Yeye cooler is built with cutting-edge technology that doesn't compromise on style.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                title: "Crystal-Clear Shell",
                description: "Military-grade Tritan™ material provides unmatched clarity and durability.",
                icon: "🔍",
              },
              {
                title: "Double-Wall Insulation",
                description: "Advanced vacuum insulation keeps contents cold for up to 72 hours.",
                icon: "❄️",
              },
              {
                title: "Modular Design",
                description: "Customizable accessories and components adapt to any adventure.",
                icon: "🔧",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow bg-card border-border">
                  <CardContent className="p-8 text-center">
                    <div className="text-4xl mb-4">{feature.icon}</div>
                    <h3 className="text-xl font-semibold mb-4 text-card-foreground">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mt-16 text-center px-4"
          >
            <OptimizedImage
              src="/images/technical-diagram.png"
              alt="Technical diagram"
              width={800}
              height={400}
              className="w-full max-w-2xl mx-auto rounded-2xl aspect-[2/1]"
            />
          </motion.div>
        </div>
      </section>

      {/* JUG Product Spotlight */}
      {products && products.find((p: Product) => p.id === "yeye-jug") && (
        <section className="py-24 bg-background">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <Badge
                className={`mb-4 ${variant === "pink" ? "bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-200 border-pink-200 dark:border-pink-800" : "bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border-orange-200 dark:border-orange-800"}`}
              >
                Also from Yeye
              </Badge>
              <h2 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">
                The JUG™
                {variant === "pink" && <span className="text-pink-500 ml-4">Pink Edition</span>}
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Pro-grade hydration meets transparent design. Built for the toughest adventures.
              </p>
            </motion.div>

            {(() => {
              const jugProduct = products.find((p: Product) => p.id === "yeye-jug")
              if (!jugProduct) return null

              return (
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  <motion.div
                    key={`jug-${variant}`}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                    className="relative"
                  >
                    <div className="aspect-square rounded-3xl overflow-hidden">
                      <ProductImage
                        src={getProductImage(jugProduct)}
                        alt={`Yeye JUG ${variant === "pink" ? "- Pink Edition" : ""}`}
                        width={600} // Added explicit width/height for img tag
                        height={600}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div className="absolute top-4 left-4">
                      <Badge
                        className={
                          variant === "pink"
                            ? "bg-pink-500 text-white"
                            : "bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border-orange-200 dark:border-orange-800"
                        }
                      >
                        {jugProduct.badge}
                      </Badge>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    className="space-y-6"
                  >
                    <div>
                      <h3 className="text-4xl font-bold mb-2 text-foreground">
                        {jugProduct.name}
                        {variant === "pink" && <span className="text-pink-500 ml-2">Pink</span>}
                      </h3>
                      <p className="text-xl text-muted-foreground mb-4">{jugProduct.description}</p>
                      <div className="text-2xl font-bold mb-2 text-foreground">{jugProduct.capacity}</div>
                    </div>

                    <ul className="space-y-2">
                      {jugProduct.features.slice(0, 5).map((feature: string, index: number) => (
                        <li key={index} className="flex items-center gap-2">
                          <div
                            className={`w-1.5 h-1.5 rounded-full ${variant === "pink" ? "bg-pink-500" : "bg-primary"}`}
                          />
                          <span className="text-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-3xl font-bold text-foreground">${jugProduct.price}</span>
                        <span className="text-xl text-muted-foreground line-through">${jugProduct.originalPrice}</span>
                      </div>
                      <Badge variant="destructive">
                        Save{" "}
                        {Math.round(((jugProduct.originalPrice - jugProduct.price) / jugProduct.originalPrice) * 100)}%
                      </Badge>
                    </div>

                    <div className="flex gap-3">
                      <Button
                        size="lg"
                        className={`flex-1 ${variant === "pink" ? "bg-pink-500 hover:bg-pink-600" : "bg-primary hover:bg-primary/90"} text-white`}
                        onClick={() => handleAddToCart(jugProduct)}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Preorder JUG™
                        {variant === "pink" && " Pink"}
                      </Button>
                      <Button variant="outline" size="lg" className="border-border hover:bg-accent bg-transparent">
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>

                    <Button variant="ghost" asChild className="w-full text-muted-foreground hover:text-foreground">
                      <Link href="/products/yeye-jug">
                        View Full Details
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </motion.div>
                </div>
              )
            })()}
          </div>
        </section>
      )}

      {/* Lifestyle Gallery */}
      <section className="py-24 bg-background">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-foreground">Live Transparently</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From beach days to mountain adventures, see how Yeye fits into every lifestyle.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {lifestyleImages.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="relative aspect-square rounded-2xl overflow-hidden group cursor-pointer"
              >
                <OptimizedImage
                  src={image.src}
                  alt={image.alt}
                  width={400} // Added explicit width/height for img tag
                  height={400}
                  className="object-cover w-full h-full group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300" />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Trust Signals */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="flex flex-col items-center"
            >
              <Truck className="h-12 w-12 mb-4 text-primary" />
              <h3 className="font-semibold mb-2 text-foreground">Free Shipping</h3>
              <p className="text-muted-foreground">On all orders worldwide</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="flex flex-col items-center"
            >
              <RotateCcw className="h-12 w-12 mb-4 text-primary" />
              <h3 className="font-semibold mb-2 text-foreground">30-Day Returns</h3>
              <p className="text-muted-foreground">No questions asked</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="flex flex-col items-center"
            >
              <Shield className="h-12 w-12 mb-4 text-primary" />
              <h3 className="font-semibold mb-2 text-foreground">2-Year Warranty</h3>
              <p className="text-muted-foreground">Full coverage included</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-background border-t border-border">
        <div className="container mx-auto px-4 py-16">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4 text-foreground">Yeye™</h3>
              <p className="text-muted-foreground mb-4">
                Cooler, but clearer. The revolutionary transparent cooler collection.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Products</h4>
              <ul className="space-y-2 text-muted-foreground">
                {products?.map((product: Product) => (
                  <li key={product.id}>
                    <Link href={`/products/${product.id}`} className="hover:text-foreground transition-colors">
                      {product.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Company</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <Link href="/about" className="hover:text-foreground transition-colors">
                    About
                  </Link>
                </li>
                <li>
                  <Link href="/journal" className="hover:text-foreground transition-colors">
                    Journal
                  </Link>
                </li>
                <li>
                  <Link href="/sustainability" className="hover:text-foreground transition-colors">
                    Sustainability
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Support</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <Link href="/contact" className="hover:text-foreground transition-colors">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link href="/shipping" className="hover:text-foreground transition-colors">
                    Shipping
                  </Link>
                </li>
                <li>
                  <Link href="/returns" className="hover:text-foreground transition-colors">
                    Returns
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-muted-foreground">© 2024 Yeye. All rights reserved.</p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground transition-colors">
                  Privacy
                </Link>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground transition-colors">
                  Terms
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
