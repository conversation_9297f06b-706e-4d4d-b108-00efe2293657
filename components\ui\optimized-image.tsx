"use client"
import { cn } from "@/lib/utils"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  // Removed Next.js Image specific props for direct loading
  // fill?: boolean
  // sizes?: string
  // priority?: boolean
  // placeholder?: "blur" | "empty"
  // blurDataURL?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  // Removed Next.js Image specific props for direct loading
  // fill = false,
  // sizes,
  // priority = false,
  // placeholder = "empty",
  // blurDataURL,
  ...props
}: OptimizedImageProps) {
  // Removed loading and error state logic for direct loading
  // const [isLoading, setIsLoading] = useState(true)
  // const [hasError, setHasError] = useState(false)

  // const handleLoad = () => {
  //   setIsLoading(false)
  // }

  // const handleError = () => {
  //   setIsLoading(false)
  //   setHasError(true)
  // }

  // Fallback image (can be simplified or removed if not needed for basic img)
  // const fallbackSrc = "/placeholder.svg?height=400&width=400&text=Image+Loading"

  // if (hasError) {
  //   return (
  //     <div className={cn("bg-muted flex items-center justify-center", className)}>
  //       <div className="text-muted-foreground text-sm">Failed to load image</div>
  //     </div>
  //   )
  // }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Removed loading spinner for direct loading */}
      {/* {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-r from-muted via-muted/50 to-muted animate-pulse rounded-lg flex items-center justify-center">
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      )} */}

      {/* Using standard <img> tag instead of Next.js Image */}
      <img
        src={src || "/placeholder.svg"} // Use src directly
        alt={alt}
        width={width}
        height={height}
        // Removed Next.js Image specific props
        // fill={fill}
        // sizes={sizes}
        // priority={priority}
        // placeholder={placeholder}
        // blurDataURL={blurDataURL}
        className={cn(
          // Removed opacity transition for direct loading
          // "transition-opacity duration-300",
          // isLoading ? "opacity-0" : "opacity-100",
          // fill ? "object-cover" : "",
          "object-cover w-full h-full", // Ensure image covers container
          className,
        )}
        // Removed onLoad and onError for direct loading
        // onLoad={handleLoad}
        // onError={handleError}
        {...props}
      />
    </div>
  )
}

// Preset configurations for common use cases - simplified to pass basic props
export function ProductImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return <OptimizedImage src={src} alt={alt} className={className} width={width} height={height} {...props} />
}

export function HeroImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return <OptimizedImage src={src} alt={alt} className={className} width={width} height={height} {...props} />
}

export function ThumbnailImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return <OptimizedImage src={src} alt={alt} className={className} width={width} height={height} {...props} />
}
