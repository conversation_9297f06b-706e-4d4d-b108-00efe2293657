const CACHE_NAME = "yeye-cooler-v1"
const urlsToCache = [
  "/",
  "/manifest.json",
  "/images/hero-beach.jpg",
  "/images/cooler-family.png",
  "/images/cooler-lime-studio.jpg",
  "/images/cooler-minimal.png",
  "/images/sunset-dock.jpg",
  "/images/yeye-jug-45.jpg",
  "/images/technical-diagram.png",
  "/images/pool-party.jpg",
  "/images/group-picnic.jpg",
  "/images/yeye-jug-lifestyle.jpg",
]

self.addEventListener("install", (event) => {
  event.waitUntil(caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache)))
})

self.addEventListener("fetch", (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      if (response) {
        return response
      }
      return fetch(event.request)
    }),
  )
})
