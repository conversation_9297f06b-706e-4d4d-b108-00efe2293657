// Centralized image path management
export const imagePaths = {
  // Product images - organized by product ID
  products: {
    "yeye-go": {
      main: "/images/products/yeye-go/main.jpg",
      lifestyle: ["/images/products/yeye-go/lifestyle-1.jpg", "/images/products/yeye-go/lifestyle-2.jpg"],
      details: ["/images/products/yeye-go/detail-1.jpg"],
    },
    "yeye-classic": {
      main: "/images/products/yeye-classic/main.jpg",
      lifestyle: ["/images/products/yeye-classic/lifestyle-1.jpg", "/images/products/yeye-classic/lifestyle-2.jpg"],
      details: ["/images/products/yeye-classic/detail-1.jpg"],
    },
    "yeye-weekender": {
      main: "/images/products/yeye-weekender/main.jpg",
      lifestyle: ["/images/products/yeye-weekender/lifestyle-1.jpg", "/images/products/yeye-weekender/lifestyle-2.jpg"],
      details: ["/images/products/yeye-weekender/detail-1.jpg"],
    },
    "yeye-jug": {
      main: "/images/products/yeye-jug/main.jpg",
      lifestyle: ["/images/products/yeye-jug/lifestyle-1.jpg", "/images/products/yeye-jug/lifestyle-2.jpg"],
      details: ["/images/products/yeye-jug/detail-1.jpg"],
    },
  },

  // Hero and lifestyle images
  hero: {
    main: "/images/hero/beach-main.jpg",
    secondary: "/images/hero/lifestyle-grid.jpg",
  },

  // Category images
  categories: {
    coolers: "/images/categories/coolers.jpg",
    jugs: "/images/categories/jugs.jpg",
    accessories: "/images/categories/accessories.jpg",
  },

  // Testimonial images
  testimonials: {
    sarah: "/images/testimonials/sarah.jpg",
    mike: "/images/testimonials/mike.jpg",
    emma: "/images/testimonials/emma.jpg",
  },

  // Technical and brand images
  technical: {
    diagram: "/images/technical/diagram.png",
    materials: "/images/technical/materials.jpg",
    construction: "/images/technical/construction.jpg",
  },

  // Brand assets
  brand: {
    logo: "/images/brand/logo.svg",
    logoMark: "/images/brand/logo-mark.svg",
    favicon: "/images/brand/favicon.ico",
  },
}

// Helper function to get product images
export const getProductImages = (productId: string) => {
  const product = imagePaths.products[productId as keyof typeof imagePaths.products]
  if (!product) return []

  return [product.main, ...product.lifestyle, ...product.details]
}

// Helper function to get image with fallback
export const getImageWithFallback = (imagePath: string, fallback = "/images/placeholder.jpg") => {
  return imagePath || fallback
}
