"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { Heart, ShoppingCart, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Price } from "@/components/ui/price"
import type { Product } from "@/types/product"
import { useCart } from "@/hooks/use-cart"
import { useState } from "react"

interface ProductCardProps {
  product: Product
  priority?: boolean
  className?: string
}

export function ProductCard({ product, priority = false, className }: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const { addToCart } = useCart()

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      quantity: 1,
    })
  }

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsWishlisted(!isWishlisted)
  }

  return (
    <motion.div whileHover={{ y: -4 }} transition={{ duration: 0.2 }} className={className}>
      <Card className="group overflow-hidden border-border hover:shadow-lg transition-all duration-300">
        <Link href={`/products/${product.id}`}>
          <div className="relative aspect-square overflow-hidden">
            <Image
              src={product.images[0] || "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              priority={priority}
            />

            {/* Badges */}
            <div className="absolute top-3 left-3">
              <Badge className="bg-primary text-primary-foreground">{product.badge}</Badge>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full backdrop-blur-md bg-white/80 dark:bg-black/80"
                onClick={handleWishlist}
              >
                <Heart className={`h-4 w-4 ${isWishlisted ? "fill-red-500 text-red-500" : ""}`} />
              </Button>
            </div>

            {/* Preorder Badge */}
            {product.preorder && (
              <div className="absolute bottom-3 left-3">
                <Badge variant="outline" className="backdrop-blur-md bg-white/80 dark:bg-black/80">
                  Preorder
                </Badge>
              </div>
            )}
          </div>

          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.floor(product.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs text-muted-foreground">({product.reviews})</span>
              </div>

              <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                {product.name}
              </h3>

              <p className="text-sm text-muted-foreground line-clamp-2">{product.description}</p>

              <div className="flex items-center justify-between">
                <Price price={product.price} originalPrice={product.originalPrice} size="sm" />
                <span className="text-sm font-medium text-muted-foreground">{product.capacity}</span>
              </div>
            </div>

            <Button
              className="w-full mt-4 bg-primary text-primary-foreground hover:bg-primary/90"
              onClick={handleAddToCart}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {product.preorder ? "Preorder Now" : "Add to Cart"}
            </Button>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  )
}
