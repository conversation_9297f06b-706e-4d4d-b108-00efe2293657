"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Heart, ShoppingCart, Star, Truck, Shield, RotateCcw, Ruler, Eye, Share2 } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { ProductViewer360 } from "@/components/product-viewer-360"
import { SizeComparison } from "@/components/size-comparison"
import { CustomerReviews } from "@/components/customer-reviews"

const products = {
  "yeye-go": {
    id: "yeye-go",
    name: "Yeye GO™",
    capacity: "10L",
    price: 199,
    originalPrice: 249,
    rating: 4.8,
    reviews: 324,
    badge: "Perfect for Solo",
    description: "The compact companion for personal adventures. Crystal-clear transparency meets portable perfection.",
    features: [
      "Ultra-portable 10L capacity",
      "Perfect for lunch trips and day outings",
      "Lightweight yet durable construction",
      "Fits in most car cup holders",
      "Easy single-hand operation",
    ],
    specifications: {
      Capacity: "10 Liters",
      Dimensions: "25 × 18 × 20 cm",
      Weight: "1.2 kg",
      Material: "BPA-free Tritan™",
      Insulation: "Double-wall vacuum",
      "Ice Retention": "Up to 24 hours",
    },
    images: ["/images/cooler-lime-studio.jpg", "/images/hero-beach.jpg", "/images/pool-party.jpg"],
    lifestyle: "Perfect for solo beach days, lunch breaks, and personal adventures.",
  },
  "yeye-classic": {
    id: "yeye-classic",
    name: "Yeye CLASSIC™",
    capacity: "18L",
    price: 299,
    originalPrice: 349,
    rating: 4.9,
    reviews: 1247,
    badge: "Most Popular",
    description: "The original all-rounder that started it all. The perfect balance of size, style, and functionality.",
    features: [
      "Versatile 18L capacity",
      "Ideal for couples and small groups",
      "Modular accessory system",
      "Premium crystal-clear shell",
      "Ergonomic carry handles",
    ],
    specifications: {
      Capacity: "18 Liters",
      Dimensions: "35 × 25 × 28 cm",
      Weight: "2.1 kg",
      Material: "BPA-free Tritan™",
      Insulation: "Double-wall vacuum",
      "Ice Retention": "Up to 48 hours",
    },
    images: ["/images/cooler-minimal.png", "/images/sunset-dock.jpg", "/images/group-picnic.jpg"],
    lifestyle: "The go-to choice for couples, small gatherings, and weekend adventures.",
  },
  "yeye-weekender": {
    id: "yeye-weekender",
    name: "Yeye WEEKENDER™",
    capacity: "35L",
    price: 399,
    originalPrice: 449,
    rating: 4.9,
    reviews: 892,
    badge: "Family Favorite",
    description: "Built for the big adventures. When you need space for the whole crew, the WEEKENDER delivers.",
    features: [
      "Spacious 35L capacity",
      "Perfect for families and groups",
      "Heavy-duty construction",
      "Multiple carrying options",
      "Integrated bottle opener",
    ],
    specifications: {
      Capacity: "35 Liters",
      Dimensions: "45 × 32 × 35 cm",
      Weight: "3.2 kg",
      Material: "BPA-free Tritan™",
      Insulation: "Triple-wall vacuum",
      "Ice Retention": "Up to 72 hours",
    },
    images: ["/images/cooler-family.png", "/images/pool-party.jpg", "/images/group-picnic.jpg"],
    lifestyle: "Designed for family trips, group outings, and extended adventures.",
  },
  "yeye-jug": {
    id: "yeye-jug",
    name: "Yeye JUG™",
    capacity: "1 Gallon",
    price: 149,
    originalPrice: 179,
    rating: 4.7,
    reviews: 156,
    badge: "Pro Series",
    description: "Pro-grade hydration meets transparent design. Built for the toughest adventures.",
    features: [
      "Industrial-grade construction",
      "1 gallon (3.8L) capacity",
      "Rugged latching system",
      "Wide-mouth design",
      "Leak-proof guarantee",
    ],
    specifications: {
      Capacity: "1 Gallon (3.8L)",
      Dimensions: "20 × 20 × 30 cm",
      Weight: "1.8 kg",
      Material: "Military-grade Tritan™",
      Insulation: "Double-wall vacuum",
      Temperature: "Hot/Cold retention",
    },
    images: ["/images/yeye-jug-studio.jpg", "/images/yeye-jug-lifestyle.jpg", "/images/yeye-jug-closeup.jpg"],
    lifestyle: "Perfect for construction sites, camping, and extreme outdoor activities.",
  },
}

export default function ProductPage({ params }: { params: { slug: string } }) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [showSizeComparison, setShowSizeComparison] = useState(false)
  const [show360View, setShow360View] = useState(false)
  const { addToCart } = useCart()

  const product = products[params.slug as keyof typeof products]

  if (!product) {
    return <div>Product not found</div>
  }

  const handleAddToCart = () => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      quantity: 1,
    })
  }

  const savings = product.originalPrice - product.price
  const savingsPercent = Math.round((savings / product.originalPrice) * 100)

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-24 pb-8">
        <nav className="text-sm text-muted-foreground">
          <span>Home</span> / <span>Products</span> / <span className="text-foreground">{product.name}</span>
        </nav>
      </div>

      {/* Product Hero */}
      <div className="container mx-auto px-4 pb-16">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square rounded-3xl overflow-hidden bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20">
              <motion.img
                key={selectedImage}
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
                initial={{ opacity: 0, scale: 1.1 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              />

              {/* Action Buttons */}
              <div className="absolute top-4 right-4 flex gap-2">
                <Button
                  size="icon"
                  variant="secondary"
                  className="rounded-full backdrop-blur-md bg-white/80 dark:bg-black/80"
                  onClick={() => setShow360View(true)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  size="icon"
                  variant="secondary"
                  className="rounded-full backdrop-blur-md bg-white/80 dark:bg-black/80"
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>

              {/* Badge */}
              <div className="absolute top-4 left-4">
                <Badge variant="secondary" className="backdrop-blur-md bg-white/80 dark:bg-black/80">
                  {product.badge}
                </Badge>
              </div>
            </div>

            {/* Thumbnail Images */}
            <div className="flex gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square w-20 rounded-xl overflow-hidden border-2 transition-all ${
                    selectedImage === index
                      ? "border-primary ring-2 ring-primary/20"
                      : "border-border hover:border-primary/50"
                  }`}
                >
                  <img
                    src={image || "/placeholder.svg"}
                    alt={`${product.name} view ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-4xl font-bold mb-2">{product.name}</h1>
              <p className="text-xl text-muted-foreground mb-4">{product.description}</p>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < Math.floor(product.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                    />
                  ))}
                </div>
                <span className="font-medium">{product.rating}</span>
                <span className="text-muted-foreground">({product.reviews} reviews)</span>
              </div>
            </div>

            {/* Pricing */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold">${product.price}</span>
                <span className="text-xl text-muted-foreground line-through">${product.originalPrice}</span>
                <Badge variant="destructive">Save {savingsPercent}%</Badge>
              </div>
              <p className="text-sm text-muted-foreground">Early bird pricing - limited time offer</p>
            </div>

            {/* Key Features */}
            <div className="space-y-3">
              <h3 className="font-semibold">Key Features:</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <div className="flex gap-3">
                <Button onClick={handleAddToCart} className="flex-1 h-12">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart - ${product.price}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-12 w-12 bg-transparent"
                  onClick={() => setIsWishlisted(!isWishlisted)}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? "fill-red-500 text-red-500" : ""}`} />
                </Button>
              </div>

              <Button variant="outline" onClick={() => setShowSizeComparison(true)} className="w-full">
                <Ruler className="h-4 w-4 mr-2" />
                Compare Sizes
              </Button>
            </div>

            {/* Trust Signals */}
            <div className="grid grid-cols-3 gap-4 pt-6 border-t">
              <div className="text-center">
                <Truck className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-xs font-medium">Free Shipping</p>
              </div>
              <div className="text-center">
                <RotateCcw className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-xs font-medium">30-Day Returns</p>
              </div>
              <div className="text-center">
                <Shield className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-xs font-medium">2-Year Warranty</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="container mx-auto px-4 pb-16">
        <Tabs defaultValue="specifications" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="specifications">Specifications</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="care">Care Guide</TabsTrigger>
          </TabsList>

          <TabsContent value="specifications" className="mt-8">
            <Card>
              <CardContent className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-4">Technical Specifications</h3>
                    <dl className="space-y-3">
                      {Object.entries(product.specifications).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <dt className="text-muted-foreground">{key}:</dt>
                          <dd className="font-medium">{value}</dd>
                        </div>
                      ))}
                    </dl>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-4">Perfect For</h3>
                    <p className="text-muted-foreground">{product.lifestyle}</p>
                    <img src="/images/technical-diagram.png" alt="Technical diagram" className="mt-4 rounded-lg" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="mt-8">
            <CustomerReviews productId={product.id} />
          </TabsContent>

          <TabsContent value="care" className="mt-8">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">Care Instructions</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Cleaning</h4>
                    <p className="text-sm text-muted-foreground">
                      Rinse with warm water and mild soap. For deep cleaning, use a mixture of baking soda and water.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Storage</h4>
                    <p className="text-sm text-muted-foreground">
                      Store with the lid slightly open to allow air circulation and prevent odors.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Maintenance</h4>
                    <p className="text-sm text-muted-foreground">
                      Check seals regularly and replace if damaged. Avoid extreme temperature changes.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <AnimatePresence>
        {show360View && <ProductViewer360 product={product} onClose={() => setShow360View(false)} />}
        {showSizeComparison && <SizeComparison currentProduct={product} onClose={() => setShowSizeComparison(false)} />}
      </AnimatePresence>
    </div>
  )
}
